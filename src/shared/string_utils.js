import format from 'date-fns/format'
import { Base64 } from 'js-base64'
import { formatMoney, formatNumber as accountingFormatNumber } from 'accounting'
import parsePhoneNumber from 'libphonenumber-js'

/**
 * Replace the accented characters by their non-accented equivalent
 *
 * @param in_str {String}
 * @return {String} New string without accents
 */
export const accentFold = (in_str) => {
    return in_str.replace(
        /([àáâãäå])|([ç])|([èéêë])|([ìíîï])|([ñ])|([òóôõöø])|([ß])|([ùúûü])|([ÿ])|([æ])/g,
        (str, a, c, e, i, n, o, s, u, y, ae) => {
            if (a) return 'a'
            if (c) return 'c'
            if (e) return 'e'
            if (i) return 'i'
            if (n) return 'n'
            if (o) return 'o'
            if (s) return 's'
            if (u) return 'u'
            if (y) return 'y'
            if (ae) return 'ae'
        },
    )
}

/**
 * Test if container string contains contained string, insensitive to case and accents
 *
 * @param container {String}
 * @param contained {String}
 * @return {boolean}
 */
export const icontains = (container, contained) =>
    accentFold(container.toLowerCase()).indexOf(accentFold(contained.toLowerCase())) > -1

/**
 * Replace the UTF-8 URI encoded value by its equivalent in ISO world.
 * Be aware that it does not convert all existing possibilities, some may be missing.
 *
 * @param in_str {String}
 * @return {String} New string
 */
export const transliterateEncodedUriToIso = (in_str) => {
    return in_str.replace(/(%C3%80)|(%C3%A0)|(%C3%A9)|(%C3%B4)/g, (str, c1, c2, c3, c4) => {
        if (c1) return '%C0' // À
        if (c2) return '%E0' // à
        if (c3) return '%E9' // é
        if (c4) return '%F4' // ô
    })
}

/**
 * Format given string to date formatted with french locale DD/MM/YYYY
 * https://developer.mozilla.org/fr/docs/Web/JavaScript/Reference/Objets_globaux/DateTimeFormat
 *
 * @param value
 * @param {DateTimeFormatOptions} custom_options
 * @returns {string}
 */
export const formatDate = (value, custom_options = undefined) => {
    if (!value) {
        return ''
    }

    const date = new Date(value)
    const options = custom_options || {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
    }

    if (/\+00:00/.test(value)) {
        options.timeZone = 'UTC'
    }

    return new Intl.DateTimeFormat('fr-FR', options).format(date)
}

/**
 * Api with timestamp require date to be formatted specifically in the DateTimeZone format
 * following the RFC3339 (Y-m-d\TH:i:sP)
 * This is a little bit restrictive if i may say so.
 *
 * @param {Date} date
 * @returns {string}
 */
export const formatDateToRFC3339 = (date) => {
    return format(date, 'YYYY-MM-DDTHH:mm:ssZ')
}

/**
 * Take a shallow object as parameter and give back a string url ready
 *
 * @param obj
 * @returns {string}
 */
export const makeUrlQueryString = (obj) => {
    return Object.keys(obj)
        .map((k) => `${k}=${encodeURIComponent(obj[k])}`)
        .join('&')
}

/**
 * Capitalize the first letter of a string
 *
 * @param string
 * @return {string}
 */
export const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1)
}

/**
 * Encode search parameters (object) into a string which can be parsed again by the store (see convertUrlEncodedValue)
 *
 * @param {Object} obj
 * @return {string}
 */
export const encodeSearchParams = (obj) => {
    return Base64.encode(JSON.stringify(obj))
}

/**
 * For user readability, format the location path as **.**.*$**.**.**
 *
 * @param string
 * @returns {string}
 */
export const formatLocationPath = (string) => {
    const splitted_path = string.split('.')
    let parent_path =
        splitted_path.length > 3 ? splitted_path[0] + '.' + splitted_path[1] + '.' + splitted_path[2] : null

    return parent_path
        ? parent_path.toUpperCase() + '' + string.replace(parent_path, '').replace('.', '$')
        : string.toUpperCase()
}

/**
 * Format given number
 *
 * By default, numbers are formatted to have
 * - 2 decimals
 * - a comma as decimal separator
 * - a space as thousand separator
 *
 * @param value
 * @param custom_options
 * @returns {string}
 */
export const formatNumber = (value, custom_options = {}) => {
    return accountingFormatNumber(
        value,
        Object.assign(
            {
                decimal: ',',
                thousand: ' ',
                precision: 2,
            },
            custom_options,
        ),
    )
}

/**
 * Format given value to nicely formatted € currency
 *
 * @param value
 * @returns string
 */
export const formatCurrency = (value) => {
    if (typeof value === 'string') {
        value = parseFloat(`${value}`.replace(',', '.').replace(' ', ''))
    }

    return formatMoney(value, {
        symbol: '€',
        format: '%v %s',
        decimal: ',',
        thousand: ' ',
        precision: 2,
    })
}

/**
 * Prepend CDN to the given path
 * @param {?string} path
 * @return {string}
 */
export const prependCdnToPath = (path) => {
    return path ? `${import.meta.env.VITE_APP_CDN_STATIC_IMAGES}${path}` : null
}

/**
 * Format currency string to float
 * @param value
 * @returns {number}
 */
export const formatCurrencyToFloat = (value) => {
    return parseFloat(value.replaceAll(' ', '').replaceAll(' ', '').replaceAll('€', '').replaceAll(',', '.'))
}

export const formatPhoneNumber = (value, country_code = 'FR') => {
    if ('' === value) {
        return ''
    }

    const phoneNumber = parsePhoneNumber(value, country_code)
    if (phoneNumber) {
        return phoneNumber.formatInternational()
    }

    return value
}

export function stringToDateObject(value) {
    return 'string' === typeof value ? new Date(value) : value
}
