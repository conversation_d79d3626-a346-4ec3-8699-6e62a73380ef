<script setup lang="ts">
import { DELIVERY_NOTE_WRITE } from '@/apps/erp/permissions'
import { useAuthenticationStore } from '@/stores/authentication'
import { useRoute } from 'vue-router/composables'
import { usePager } from '@/shared/composable/usePager'
import { computed, onMounted, ref } from 'vue'
import ErpPage from '@/shared/components/layout/ErpPage.vue'
import ErpDashboard from '@/shared/components/table/ErpDashboard.vue'
import { ErpButton, ErpTable } from '@/shared/components'
import {
    cPostShipmentDeliveryNotes,
    cPostShipments,
    cPostShipmentMethods,
    detachDeliveryNote,
    type ShipmentDeliveryNote,
    type Shipment,
    type ShipmentMethod,
    putWmsParcelNumber,
} from '@/shared/api/erp_server'
import { cPostPrinters, type Printer } from '@/shared/api/erp_server/print/cPostPrinters'
import ScopedBadge from '@/shared/components/ui/ScopedBadge.vue'
import ErpLink from '@/shared/components/link/ErpLink.vue'
import ErpInlineForm from '@/shared/components/form/ErpInlineForm.vue'
import ErpInputWithAddOn from '@/shared/components/form/ErpInputWithAddOn.vue'
import ErpSelect from '@/shared/components/form/ErpSelect.vue'
import SlideOutContainer from '@/shared/components/ui/SlideOutContainer.vue'
import {
    faExclamation,
    faFilePdf,
    faCheck,
    faPrint,
    faPencil,
    faTimes,
    // @ts-ignore
} from '@son-video/font-awesome-pro-solid/index.es'
import PageHeader from '@/shared/components/ui/PageHeader.vue'
import ShipmentDeliveryNoteEditForm from '@/apps/erp/components/ShipmentDeliveryNote/ShipmentDeliveryNoteEditForm.vue'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'
import Alert from '@/shared/components/ui/Alert.vue'
import { useToastStore } from '@/services/plugin/toast/stores'
import { i18n } from '@/services/config/i18n'
import { type Country, cPostCountries } from '@/shared/api/erp_server/country/cPostCountries'
import type { ErpTableColumnDefinition, ErpTableRow } from '@/shared/components/ErpTable'
import postDeliveryNoteStickersPrint from '@/shared/api/erp_server/shipment/PostDeliveryNoteStickersPrint'
import getStickers from '@/shared/api/erp_server/shipment/GetStickers'
import { useBase64Tools } from '@/shared/composable/useBase64Tools'

const route = useRoute()

const is_loading = ref(false)

const columns = ref<ErpTableColumnDefinition[]>([
    {
        title: '#',
        name: 'delivery_note_id',
    },
    {
        title: 'N° colis',
        name: 'parcels',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        title: 'Nb colis',
        name: 'parcel_quantity',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        title: 'Poids',
        name: 'parcel_weight',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        title: 'Transporteur',
        name: 'shipment_method',
    },
    {
        title: 'Destination',
        name: 'destination',
    },
    {
        title: 'Statut',
        name: 'status',
    },
    {
        title: 'Étiquettes',
        name: 'stickers',
    },
])
const restricted_colums: ErpTableColumnDefinition[] = [
    {
        title: 'Action',
        name: 'action',
    },
]

const rows = ref<ShipmentDeliveryNote[]>([])
const shipment = ref<Shipment>()
const printers = ref<Printer[]>([])
const countries = ref<Country[]>([])
const shipment_methods = ref<ShipmentMethod[]>([])

const edited_delivery_note = ref<ShipmentDeliveryNote>()
const delivery_note_id_to_delete = ref<number>()
const action_in_progress = ref<boolean>(false)

const { page, total_items, pagination, limit } = usePager()
limit.value = 999

const printers_options = computed(() => {
    return printers.value.map((p) => ({ value: p.printer_id, label: p.name }))
})

const fetchData = async (new_page = 1) => {
    is_loading.value = true

    try {
        const request_shipment = async () => {
            if (shipment.value === undefined) {
                const response = await cPostShipments({
                    where: {
                        _and: [{ shipment_id: { _eq: route.params.shipment_id } }],
                    },
                })

                shipment.value = response.data.shipments.shift()
                if (shipment.value?.status === 1 && can_write_delivery_note.value) {
                    columns.value.push(...restricted_colums)
                }
            }
        }

        const request_delivery_notes = async () => {
            const response = await cPostShipmentDeliveryNotes(parseInt(route.params.shipment_id), {
                page: new_page,
                limit: limit.value,
            })

            rows.value = response.data.shipment_delivery_notes
            page.value = response.data._pager?.page || 1
            total_items.value = response.data._pager?.total || 0
        }

        const request_printers = async () => {
            if (printers.value.length === 0) {
                const response = await cPostPrinters({
                    where: {
                        _and: [{ type: { _eq: '102x152' } }, { status: { _eq: 'active' } }],
                    },
                })
                printers.value = response.data.printers
            }
        }

        const request_countries = async () => {
            if (countries.value.length === 0 && can_write_delivery_note.value) {
                const response = await cPostCountries({ limit: 999 })
                countries.value = response.data.countries
            }
        }

        await Promise.all([request_delivery_notes(), request_shipment(), request_printers(), request_countries()])
    } catch (error) {
        useToastStore().add({ content: 'Une erreur est survenue, veuillez réessayer.' })
    } finally {
        is_loading.value = false
    }

    if (shipment.value !== undefined && shipment_methods.value.length === 0 && can_write_delivery_note.value) {
        const response_shipment_methods = await cPostShipmentMethods({
            where: {
                _and: [{ is_active: { _eq: true } }, { carrier_id: { _eq: shipment.value.carrier_id } }],
            },
        })
        shipment_methods.value = response_shipment_methods.data.shipment_methods
    }
}

const formatStatus = (status: number) => {
    switch (status) {
        case 0:
            return 'en préparation'
        case 1:
        case 2:
            return 'en cours'
        case 4:
            return 'imprimé'
        default:
            console.warn(`shipment status "${status}" not handled`)
            return 'inconnu'
    }
}

const can_write_delivery_note = computed<boolean>(() => {
    return useAuthenticationStore().hasPermission(DELIVERY_NOTE_WRITE)
})

const getParcelsWithNumber = (row: ShipmentDeliveryNote) => {
    return row.parcels.filter((p) => !!p.number)
}

const getParcelsWithoutNumber = (row: ShipmentDeliveryNote) => {
    // empty string, null, undefined
    return row.parcels.filter((p) => !p.number)
}

const onUpdateParcelNumber = async (parcel: ShipmentDeliveryNote['parcels'][number], parcel_number: string) => {
    try {
        action_in_progress.value = true
        await putWmsParcelNumber(parcel.id, parcel_number)
        parcel.number = parcel_number
    } catch (error) {
        useToastStore().add({
            content: i18n.t('error_message:get_request_error'),
        })
    } finally {
        action_in_progress.value = false
    }
}

const onPrintStickers = async (printer_id: number, delivery_note_id: number) => {
    try {
        action_in_progress.value = true
        await postDeliveryNoteStickersPrint(printer_id, delivery_note_id)
    } catch (error) {
        useToastStore().add({
            content: i18n.t('error_message:get_request_error'),
        })
    } finally {
        action_in_progress.value = false
    }
}

const onOpenStickers = async (parcel_id: number) => {
    try {
        const response = await getStickers(parcel_id)
        if (response.content === undefined) {
            throw new Error('Unable to reach the sticker')
        }

        const blob = useBase64Tools().convertToBlob(response.content, 'application/pdf')

        window.open(URL.createObjectURL(blob))
    } catch (error) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

const onDeleteDeliveryNote = (delivery_note_id: number) => {
    delivery_note_id_to_delete.value = delivery_note_id
}

const onConfirmDeleteDeliveryNote = async () => {
    try {
        action_in_progress.value = true

        if (!delivery_note_id_to_delete.value) {
            throw Error('delivery_note id is missing')
        }

        await detachDeliveryNote(delivery_note_id_to_delete.value)

        rows.value = rows.value.filter((r) => r.delivery_note_id !== delivery_note_id_to_delete.value)

        onCloseDeleteSlideIn()
    } catch (error: any) {
        useToastStore().add({ content: error.response.data.message.value })
    } finally {
        action_in_progress.value = false
    }
}

const onCloseDeleteSlideIn = () => {
    delivery_note_id_to_delete.value = undefined
}

const onEditDeliveryNote = (row: ShipmentDeliveryNote) => {
    edited_delivery_note.value = structuredClone(row)
}

const onCloseEditDeliveryNote = (updated_delivery_note?: ShipmentDeliveryNote) => {
    if (updated_delivery_note) {
        const clone_rows = structuredClone(rows.value)
        const row_index = clone_rows.findIndex((r) => r.delivery_note_id === updated_delivery_note.delivery_note_id)
        clone_rows[row_index] = structuredClone(updated_delivery_note)

        rows.value = clone_rows
    }

    edited_delivery_note.value = undefined
}

const toShipmentDeliveryNote = (row: ErpTableRow) => row as ShipmentDeliveryNote

onMounted(() => {
    fetchData(page.value)
})
</script>

<template>
    <erp-page :title="`Détails de l'expédition ${shipment?.shipment_id ?? ''}`">
        <erp-dashboard
            :is-loading="is_loading"
            v-bind="pagination"
            @pagination:change="fetchData()"
            @pagination:refresh="fetchData()"
        >
            <erp-table no-border :columns="columns" :rows="rows" :is-loading="is_loading">
                <template #delivery_note_id="{ row }">
                    <scoped-badge scope="BL" data-context="delivery-note-badge">
                        <erp-link
                            :to="`/legacy/v1/commandes/affichage_bon_livraison.php?id_bon_livraison=${row.delivery_note_id}`"
                            target="_blank"
                        >
                            {{ row.delivery_note_id }}
                        </erp-link>
                    </scoped-badge>
                </template>

                <template #shipment_method="{ row }">
                    {{ shipment?.carrier_name }} - {{ row.shipment_method_name }}
                </template>

                <template #parcel_weight="{ row }"> {{ row.parcel_weight }} kg</template>

                <template #parcels="{ row }">
                    <template v-if="row.status == 4">
                        <ul>
                            <li v-for="parcel in getParcelsWithNumber(toShipmentDeliveryNote(row))" :key="parcel.id">
                                {{ parcel.number }}
                            </li>
                            <li
                                v-for="parcel in getParcelsWithoutNumber(toShipmentDeliveryNote(row))"
                                :key="parcel.id"
                                class="whitespace-nowrap"
                            >
                                <erp-inline-form
                                    :is-saving="action_in_progress"
                                    data-context="delivery-note-update-parcel-number"
                                    :icon="faCheck"
                                    :icon-classes="['text-slate-600']"
                                    tooltip-message="Enregistrer le numéro de colis"
                                    @submit="onUpdateParcelNumber(parcel, $event.target.parcel_number.value)"
                                >
                                    <erp-input-with-add-on
                                        placeholder="N° de colis manquant"
                                        class="min-w-[150px]"
                                        :leading-icon="faExclamation"
                                        leading-classes="text-red-600"
                                        type="text"
                                        name="parcel_number"
                                    />
                                </erp-inline-form>
                            </li>
                        </ul>
                    </template>
                    <span v-else></span>
                </template>

                <template #destination="{ row }">
                    {{ row.customer.firstname }} {{ row.customer.lastname }} <br />
                    {{ row.customer.postal_code }} {{ row.customer.city }} {{ row.customer.country }}
                </template>

                <template #status="{ row }">
                    {{ formatStatus(row.status) }}
                </template>

                <template #stickers="{ row }">
                    <div class="flex flex-wrap gap-1">
                        <erp-button
                            v-if="row.status == 4"
                            data-context="delivery-notes-show-sticker"
                            tertiary
                            sm
                            :icon="faFilePdf"
                            :icon-class="['text-red-700']"
                            target="_blank"
                            @click="onOpenStickers(row.parcels[0].id)"
                        >
                            Voir les étiquettes
                        </erp-button>

                        <template v-if="1 === shipment?.status">
                            <erp-inline-form
                                v-if="row.status > 2"
                                :is-saving="action_in_progress"
                                data-context="delivery-note-print-sticker"
                                :icon="faPrint"
                                :icon-classes="['text-blue-600']"
                                tooltip-message="Réimprimer l'étiquette"
                                @submit="
                                    onPrintStickers(parseInt($event.target.printer_id.value), row.delivery_note_id)
                                "
                            >
                                <erp-select required :options="printers_options" name="printer_id"></erp-select>
                            </erp-inline-form>
                            <span v-else class="text-red-500">Étiquette non imprimée</span>
                        </template>
                    </div>
                </template>

                <template #action="{ row }">
                    <template v-if="1 === shipment?.status">
                        <div class="flex gap-1">
                            <erp-tooltip v-if="can_write_delivery_note" message="Modifier">
                                <erp-button
                                    data-context="delivery-notes-edit"
                                    tertiary
                                    sm
                                    :icon="faPencil"
                                    :icon-class="['text-blue-600']"
                                    @click="onEditDeliveryNote(toShipmentDeliveryNote(row))"
                                />
                            </erp-tooltip>
                            <erp-tooltip v-if="can_write_delivery_note" message="Supprimer">
                                <erp-button
                                    data-context="delivery-notes-delete"
                                    :icon="faTimes"
                                    :icon-class="['text-red-500']"
                                    tertiary
                                    sm
                                    @click="onDeleteDeliveryNote(parseInt(row.delivery_note_id))"
                                />
                            </erp-tooltip>
                        </div>
                    </template>
                </template>
            </erp-table>
        </erp-dashboard>

        <slide-out-container
            v-if="edited_delivery_note"
            :is-open="true"
            @close="onCloseEditDeliveryNote(edited_delivery_note)"
        >
            <page-header>Edition du bon de livraison</page-header>

            <shipment-delivery-note-edit-form
                :initial-data="edited_delivery_note"
                :shipment-methods="shipment_methods"
                :countries="countries"
                @update="onCloseEditDeliveryNote($event)"
                @cancel="onCloseEditDeliveryNote()"
            />
        </slide-out-container>

        <slide-out-container v-if="delivery_note_id_to_delete" :is-open="true" @close="onCloseDeleteSlideIn()">
            <page-header>Suppression du bon de livraison {{ delivery_note_id_to_delete }}</page-header>
            <div class="p-3 flex flex-col gap-3" data-context="detach-delivery-note-panel">
                <alert danger hide-icon flat>
                    Confirmez vous la suppression du bon de livraison
                    <span class="font-medium">{{ delivery_note_id_to_delete }}</span
                    >&nbsp;?
                </alert>
                <div class="flex gap-3">
                    <erp-button
                        color="red"
                        xl
                        :is-loading="action_in_progress"
                        data-context="detach-delivery-note-confirm"
                        @click="onConfirmDeleteDeliveryNote()"
                    >
                        Confirmer
                    </erp-button>
                    <erp-button tertiary xl data-context="detach-delivery-note-cancel" @click="onCloseDeleteSlideIn()">
                        Annuler
                    </erp-button>
                </div>
            </div>
        </slide-out-container>
    </erp-page>
</template>
