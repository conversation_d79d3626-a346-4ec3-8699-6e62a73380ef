import { i18n } from '@/services/config/i18n'
import { useToastStore } from '@/services/plugin/toast/stores'
import getAntiFraudForCustomerOrder from '@/shared/api/erp_server/anti_fraud/getAntiFraudForCustomerOrder'
import type { AntiFraudForCustomerOrder } from '@/shared/api/erp_server/anti_fraud/types'
import { getCustomerOrderForEditionPage } from '@/shared/api/erp_server/customer_order/getCustomerOrderForEditionPage'
import type { CustomerOrderForEditionPage } from '@/shared/api/erp_server/customer_order/types'
import getPaymentV2CustomerOrderContext from '@/shared/api/erp_server/payment/getPaymentV2CustomerOrderContext'
import type {
    PaymentV2CustomerOrderContext,
    PaymentV2OperationDetails,
    PaymentV2PayByLink,
} from '@/shared/api/erp_server/payment/types'
import { useErpLocalStorage } from '@/shared/composable/useErpLocalStorage'
import type { ScalarUUID } from '@/shared/graphql/types'
import type { RemovableRef } from '@vueuse/core'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const CUSTOMER_ORDER_CONFIG_KEY: string = 'customer_order_page.config'

export const useCustomerOrderStore = defineStore('customer_order', () => {
    const customer_order = ref<CustomerOrderForEditionPage | undefined>(undefined)
    const is_loading = ref<boolean>(false)
    const anti_fraud = ref<AntiFraudForCustomerOrder | undefined>(undefined)

    const is_amazon_business = computed<boolean>(() =>
        (customer_order.value?.tags ?? []).some((t) => t.name === 'amazon_business'),
    )
    const is_duty_free = computed<boolean>(
        () => (customer_order.value?.tags ?? []).find((t) => t.name === 'amazon_business')?.meta?.HT,
    )
    const source = computed(() => (customer_order.value?.tags ?? []).find((t) => t.name.includes('source.')))

    async function load(customer_order_id: number) {
        is_loading.value = true

        try {
            const response = await getCustomerOrderForEditionPage(customer_order_id)
            customer_order.value = response.data

            const anti_fraud_response = await getAntiFraudForCustomerOrder(customer_order_id)
            anti_fraud.value = anti_fraud_response.data
        } catch (error) {
            useToastStore().add({ content: i18n.t('error_message:request_error') })
        } finally {
            is_loading.value = false
        }
    }

    const config: RemovableRef<any> = useErpLocalStorage(CUSTOMER_ORDER_CONFIG_KEY, {
        payment_block_toggle: true,
    })

    return {
        anti_fraud,
        customer_order,
        config,
        is_amazon_business,
        is_duty_free,
        source,
        is_loading,
        load,
    }
})

const customer_order_context = ref<undefined | PaymentV2CustomerOrderContext>(undefined)

export const useCustomerOrderPaymentV2Store = defineStore('customer_order_payment_v2', () => {
    const selected_operation = ref<PaymentV2OperationDetails | undefined>(undefined)
    const is_loading = ref<boolean>(false)
    const loading_error = ref<undefined | ScalarUUID>(undefined)

    // Specific to PayByLink worfflow
    const is_worldline_pay_by_link_opened = ref<boolean>(false)
    const new_pay_by_link = ref<undefined | PaymentV2PayByLink>(undefined)

    async function loadByCustomerOrderId(customer_order_id: number) {
        is_loading.value = true

        try {
            const response = await getPaymentV2CustomerOrderContext(customer_order_id)
            customer_order_context.value = response.data

            if (selected_operation.value !== undefined) {
                viewDetailsOnOperation(selected_operation.value.operation_id)
            }
        } catch (error) {
            useToastStore().add({ content: i18n.t('error_message:request_error') })
        } finally {
            is_loading.value = false
        }
    }

    function viewDetailsOnOperation(operation_id: ScalarUUID) {
        selected_operation.value =
            (customer_order_context.value?.operations ?? []).find((o) => o.operation_id === operation_id) ?? undefined

        if (!selected_operation.value) {
            loading_error.value = operation_id
        }
    }

    function closeDetailsOnOperation() {
        loading_error.value = undefined
        selected_operation.value = undefined
    }

    return {
        customer_order_context,
        selected_operation,
        is_loading,
        loading_error,
        is_worldline_pay_by_link_opened,
        new_pay_by_link,
        loadByCustomerOrderId,
        viewDetailsOnOperation,
        closeDetailsOnOperation,
    }
})
