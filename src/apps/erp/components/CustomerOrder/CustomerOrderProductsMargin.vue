<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { cPostCustomerOrderProducts, type CustomerOrderProduct } from '@/shared/api/erp_server'
import { useToastStore } from '@/services/plugin/toast/stores'
import { ErpTable } from '@/shared/components/ErpTable'
import PageHeader from '@/shared/components/ui/PageHeader.vue'
import ErpArticleItem from '@/shared/components/article/ErpArticleItem.vue'
import { formatCurrency } from '@/shared/string_utils'

interface Props {
    customerOrderId: number
}

const props = withDefaults(defineProps<Props>(), {})

const is_loading = ref<boolean>(false)
const products = ref<CustomerOrderProduct[]>([])

const columns = ref([
    {
        name: 'article',
        title: 'Produit',
    },
    {
        name: 'quantity',
        title: 'Qté',
    },
    {
        name: 'selling_price',
        title: 'Prix de vente',
    },
    {
        name: 'promo_budget',
        title: ['Budget', 'Promo'],
    },
    {
        name: 'unconditional_discount',
        title: ['Remise', 'inconditionnelle'],
    },
    {
        name: 'discount',
        title: 'Remise',
    },
])

const erp_base_url = computed<string>(() => {
    return import.meta.env.VITE_APP_ERP_BASE_URL
})

const params = computed(() => {
    return {
        where: {
            _and: [
                {
                    customer_order_id: {
                        _eq: props.customerOrderId,
                    },
                },
            ],
        },
        limit: 9999,
    }
})

/**
 * Fetch all customer order products
 */
const fetchData = async (): Promise<void> => {
    try {
        is_loading.value = true
        const response = await cPostCustomerOrderProducts(params.value)
        products.value = response.data.customer_order_products
    } catch (error) {
        useToastStore().add({ content: 'Une erreur est survenue lors de la récupération des données' })
    } finally {
        is_loading.value = false
    }
}

onMounted(() => {
    fetchData()
})
</script>

<template>
    <div v-if="!is_loading" data-context="products-availability">
        <page-header>
            <span class="mr-3">Marges produits</span>
        </page-header>

        <erp-table :columns="columns" :rows="products">
            <template #article="{ row }">
                <erp-article-item show-actions :sku="row.sku" :name="row.article_name" :image="row.article_image" />
            </template>

            <template #quantity="{ row }"> {{ row.quantity }}</template>

            <template #selling_price="{ row }">
                {{ formatCurrency(row.selling_price) }}
            </template>

            <template #promo_budget="{ row }">
                <span v-if="row.promo_budget_amount">{{ row.unconditional_discount_rate }} % </span>
                <span v-else> - </span>
            </template>

            <template #unconditional_discount="{ row }"> {{ formatCurrency(row.promo_budget_amount) }}</template>

            <template #discount="{ row }"> {{ formatCurrency(row.discount_amount) }} </template>
        </erp-table>
    </div>
</template>
