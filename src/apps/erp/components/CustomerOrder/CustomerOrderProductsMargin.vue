<template>
    <div v-if="!is_loading" data-context="products-availability">
        <page-header>
            <span class="mr-3">Marges produits</span>

            <badge :color="customer_order_availability_status.color">
                {{ customer_order_availability_status.label }}
            </badge>
        </page-header>

        <erp-table :columns="columns" :rows="mapped_products">
            <template #article="{ row }">
                <erp-article-item show-actions :sku="row.sku" :name="row.article_name" :image="row.article_image" />
            </template>

            <template #quantity="{ row }"> {{ row.availability[0].customer_order_product_quantity }} </template>

            <template #availability="{ row }">
                <badge :color="getProductAvailability(row).color"> {{ getProductAvailability(row).label }} </badge>
            </template>

            <template #expected_at="{ row }">
                <div v-if="row._availability_status === 'replenishment'" data-context="content">
                    <date-formatter :date="row.availability[0].availability_date" format="DD/MM/YYYY" />
                </div>

                <div
                    v-else-if="
                        row._availability_status === 'unknown' && (row.available_supplier_orders ?? []).length > 0
                    "
                    class="flex flex-col gap-1"
                    data-context="content"
                >
                    <scoped-badge
                        v-for="supplier_order of row.available_supplier_orders"
                        :key="supplier_order.supplier_order_id"
                        scope="cmd frn"
                    >
                        <erp-link
                            :to="`${erp_base_url}/legacy/commandeFournisseur/edition?id_commande_fournisseur=${supplier_order.supplier_order_id}`"
                            target="_blank"
                        >
                            {{ supplier_order.supplier_order_id }}
                        </erp-link>
                    </scoped-badge>
                </div>

                <div
                    v-else-if="
                        ['available', 'available_in_another_store', 'in_transfer'].includes(row._availability_status) &&
                        (row.available_transfers ?? []).length > 0
                    "
                    class="flex flex-col gap-1"
                    data-context="content"
                >
                    <scoped-badge v-for="transfer of row.available_transfers" :key="transfer.transfer_id" scope="trf">
                        <erp-link
                            :to="`${erp_base_url}/legacy/stock/bonTransfertEdit?id=${transfer.transfer_id}`"
                            target="_blank"
                        >
                            {{ transfer.transfer_id }}
                        </erp-link>
                    </scoped-badge>
                </div>

                <template v-else>&nbsp;</template>
            </template>

            <template #row_expander="{ row, column_names }">
                <tr v-if="showExpander" data-context="more-customer-orders">
                    <td :colspan="column_names.length">
                        <erp-link
                            :to="`${erp_base_url}/legacy/commande/recherche?filter=sku_attente&value=${row.sku}`"
                            target="_blank"
                            class="ml-2"
                        >
                            Voir autres commandes en attente
                        </erp-link>
                    </td>
                </tr>
            </template>
        </erp-table>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { cPostCustomerOrderProducts } from '@/shared/api/erp_server'
import Badge from '@/shared/components/ui/Badge.vue'
import ScopedBadge from '@/shared/components/ui/ScopedBadge.vue'
import { DateFormatter } from '@/shared/components'
import ErpLink from '@/shared/components/link/ErpLink.vue'
import { useToastStore } from '@/services/plugin/toast/stores'
import { ErpTable } from '@/shared/components/ErpTable'
import PageHeader from '@/shared/components/ui/PageHeader.vue'
import ErpArticleItem from '@/shared/components/article/ErpArticleItem.vue'
import { AVAILABILITY_STATUSES } from '@/apps/erp/components/CustomerOrder/constants'

// Types
interface SupplierOrder {
    supplier_order_id: number
}

interface Transfer {
    transfer_id: number
    status: string
}

interface Availability {
    customer_order_product_quantity: number
    availability_date: string | null
    store_pickup_id: number | null
    sent_quantity: number
    customer_order_status: 'valid' | 'invalid'
    warehouses_stock: number
    available_quantity_in_store: number
    store_quantity: number
}

interface CustomerOrderProduct {
    sku: string
    article_name: string
    article_image: string
    availability: Availability[]
    available_supplier_orders: SupplierOrder[] | null
    available_transfers: Transfer[] | null
}

interface MappedProduct extends CustomerOrderProduct {
    _availability_status: string
}

interface TableColumn {
    name: string
    title: string
}

interface AvailabilityStatus {
    label: string
    color: string
}

type AvailabilityStatusType =
    | 'unknown'
    | 'replenishment'
    | 'shipping_available_in_another_store'
    | 'shipping_bookable_from_another_store'
    | 'bookable_from_another_store'
    | 'available_in_another_store'
    | 'in_transfer'
    | 'bookable'
    | 'available'
    | 'available_in_store'
    | 'delivered'
    | 'delivered_from_store'

// Props
interface Props {
    showExpander?: boolean
    customerOrderId: number
}

const props = withDefaults(defineProps<Props>(), {
    showExpander: true
})

// Constants
const SORT_ORDER: AvailabilityStatusType[] = [
    'unknown',
    'replenishment',
    'shipping_available_in_another_store',
    'shipping_bookable_from_another_store',
    'bookable_from_another_store',
    'available_in_another_store',
    'in_transfer',
    'bookable',
    'available',
    'available_in_store',
    'delivered',
    'delivered_from_store',
]

// Reactive data
const is_loading = ref<boolean>(false)
const products = ref<CustomerOrderProduct[] | null>(null)

const columns: TableColumn[] = [
    {
        name: 'article',
        title: 'Produit',
    },
    {
        name: 'quantity',
        title: 'Qté',
    },
    {
        name: 'availability',
        title: 'Dispo',
    },
    {
        name: 'expected_at',
        title: 'Livraisons prévues',
    },
]
// Computed properties
const erp_base_url = computed<string>(() => {
    return import.meta.env.VITE_APP_ERP_BASE_URL
})

const params = computed(() => {
    return {
        where: {
            _and: [
                {
                    customer_order_id: {
                        _eq: props.customerOrderId,
                    },
                },
            ],
        },
        included_dependencies: ['availability', 'available_supplier_orders', 'available_transfers'],
        limit: 9999,
    }
})

/**
 * Add its availability status to each product
 */
const mapped_products = computed<MappedProduct[]>(() => {
    if (!products.value) return []

    return products.value
        .map((product) => {
            return Object.assign({}, product, {
                _availability_status: getProductAvailabilityStatus(product),
            })
        })
        .sort((a, b) => SORT_ORDER.indexOf(a._availability_status as AvailabilityStatusType) - SORT_ORDER.indexOf(b._availability_status as AvailabilityStatusType))
})
/**
 * 3 possible statuses on customer order :
 * Livrée : all products have been delivered or picked up in store
 * Livrable : all products are available for delivery or picking up in store (for valid customer order)
 * Réservable : all products are bookable for delivery or picking up in  store (for invalid customer order)
 * Non livrable : at least one product is not in stock (or not in store for pickup customer orders)
 */
const customer_order_availability_status = computed<AvailabilityStatus>(() => {
    const DELIVERED_STATUSES: AvailabilityStatusType[] = ['delivered', 'delivered_from_store']
    const DELIVERABLE_STATUSES: AvailabilityStatusType[] = [
        'available',
        'available_in_store',
        'available_in_another_store',
        'shipping_available_in_another_store',
        ...DELIVERED_STATUSES,
    ]
    const BOOKABLE_STATUSES: AvailabilityStatusType[] = [
        'bookable',
        'shipping_bookable_from_another_store',
        'bookable_from_another_store',
    ]

    if (mapped_products.value.every((p) => DELIVERED_STATUSES.includes(p._availability_status as AvailabilityStatusType))) {
        return { label: 'Expédiée / Retirée', color: 'green' }
    }

    if (mapped_products.value.every((p) => DELIVERABLE_STATUSES.includes(p._availability_status as AvailabilityStatusType))) {
        return { label: 'Livrable', color: 'green' }
    }

    if (mapped_products.value.every((p) => BOOKABLE_STATUSES.includes(p._availability_status as AvailabilityStatusType))) {
        return { label: 'Réservable', color: 'green' }
    }

    return { label: 'Non livrable', color: 'red' }
})
// Methods
/**
 * Fetch all customer order products
 */
const fetchData = async (): Promise<void> => {
    try {
        is_loading.value = true
        const response = await cPostCustomerOrderProducts(params.value)
        products.value = Object.freeze(response.data.customer_order_products).filter((product: CustomerOrderProduct) => {
            return product.availability[0].customer_order_product_quantity > 0
        })
    } catch (error) {
        useToastStore().add({ content: 'Une erreur est survenue lors de la récupération des données' })
    } finally {
        is_loading.value = false
    }
}
/**
 * 4 POSSIBLE STATUSES for a product from customer order with delivery :
 * delivered : the product has already been sent
 * available : the product is available and can be sent
 * replenishment : there is a supplier order in progress with date
 * unknown : we have no idea when the product will be replenished (with or without a knowing supplier order)
 *
 * 6 POSSIBLE STATUSES for a product from customer order with pickup from store :
 * delivered_from_store : the product has already been picked up from store
 * available_from_store : the product is in store and can be picked up
 * available_in_other_warehouse : the product is available for this customer order but still in stock in another warehouse or store (even with a transfer still not sent)
 * in_transfer : the product is available for this customer order and a transfer from another warehouse or store has already been sent
 * replenishment : there is a supplier order in progress with date
 * unknown : we have no idea when the product will be replenished (with or without a knowing supplier order)
 */
const getProductAvailabilityStatus = (product: CustomerOrderProduct): AvailabilityStatusType => {
    const AVAILABLE_DATE = '1990-01-01'
    const UNKNOWN_DATE = '2011-01-01'

    if (
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id === null &&
        product.availability[0].customer_order_product_quantity === product.availability[0].sent_quantity
    ) {
        return 'delivered'
    }
    if (
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id !== null &&
        product.availability[0].customer_order_product_quantity === product.availability[0].sent_quantity
    ) {
        return 'delivered_from_store'
    }
    if (
        product.availability[0].customer_order_status === 'valid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id === null &&
        product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
        product.availability[0].warehouses_stock >= product.availability[0].customer_order_product_quantity
    ) {
        return 'available'
    }
    if (
        product.availability[0].customer_order_status === 'invalid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id === null &&
        product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
        product.availability[0].warehouses_stock >= product.availability[0].customer_order_product_quantity
    ) {
        return 'bookable'
    }
    if (
        product.availability[0].customer_order_status === 'valid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id === null &&
        product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
        product.availability[0].warehouses_stock < product.availability[0].customer_order_product_quantity
    ) {
        return 'shipping_available_in_another_store'
    }
    if (
        product.availability[0].customer_order_status === 'invalid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id === null &&
        product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
        product.availability[0].warehouses_stock < product.availability[0].customer_order_product_quantity
    ) {
        return 'shipping_bookable_from_another_store'
    }
    if (
        product.availability[0].customer_order_status === 'valid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id !== null &&
        product.availability[0].available_quantity_in_store >=
            product.availability[0].customer_order_product_quantity
    ) {
        return 'available_in_store'
    }
    if (
        product.availability[0].customer_order_status === 'invalid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id !== null &&
        product.availability[0].available_quantity_in_store >=
            product.availability[0].customer_order_product_quantity
    ) {
        return 'bookable'
    }
    if (
        product.availability[0].customer_order_status === 'valid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id !== null &&
        product.availability[0].store_quantity !== product.availability[0].customer_order_product_quantity &&
        product.availability[0].warehouses_stock >= product.availability[0].customer_order_product_quantity &&
        (product.available_transfers === null ||
            !product.available_transfers.find((transfer) => transfer.status === 'expedie'))
    ) {
        return 'available'
    }
    if (
        product.availability[0].customer_order_status === 'valid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id !== null &&
        product.availability[0].store_quantity !== product.availability[0].customer_order_product_quantity &&
        product.availability[0].warehouses_stock < product.availability[0].customer_order_product_quantity &&
        (product.available_transfers === null ||
            !product.available_transfers.find((transfer) => transfer.status === 'expedie'))
    ) {
        return 'available_in_another_store'
    }
    if (
        product.availability[0].customer_order_status === 'invalid' &&
        product.availability[0].availability_date === AVAILABLE_DATE &&
        product.availability[0].store_pickup_id !== null &&
        product.availability[0].store_quantity !== product.availability[0].customer_order_product_quantity &&
        (product.available_transfers === null ||
            !product.available_transfers.find((transfer) => transfer.status === 'expedie'))
    ) {
        return 'bookable_from_another_store'
    }
    if (
        product.availability[0].store_pickup_id !== null &&
        product.available_transfers !== null &&
        product.available_transfers.find((transfer) => transfer.status === 'expedie')
    ) {
        return 'in_transfer'
    }
    if (
        product.availability[0].availability_date === UNKNOWN_DATE &&
        product.available_supplier_orders !== null
    ) {
        return 'unknown'
    }
    if (product.availability[0].availability_date === null) {
        return 'unknown'
    }

    return 'replenishment'
}
/**
 * Return object {label and color} from const
 */
const getProductAvailability = (mapped_product: MappedProduct): AvailabilityStatus => {
    return AVAILABILITY_STATUSES[mapped_product._availability_status as keyof typeof AVAILABILITY_STATUSES]
}

// Lifecycle
onMounted(() => {
    fetchData()
})
</script>
