describe('Customer order - Overdue', function () {
    const PAGE = '/customer-order/overdue'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/customer-orders', {
            fixture: 'erp/customer-order/overdue/customer_orders.json',
        }).as('cpost_customer_orders')

        cy.intercept('GET', '**/api/erp/v1/customer-order/statuses', {
            fixture: 'erp/customer-order/statuses.json',
        }).as('get_statuses')
    })

    describe('On page on load', function () {
        it('check table columns', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@cpost_customer_orders').then((xhr) => {
                expect(xhr.request.body.where).to.deep.equal({
                    _and: [
                        {
                            overdue_status: {
                                _in: ['NEW'],
                            },
                        },
                    ],
                })
            })
            cy.wait('@get_statuses')

            cy.get('[data-context=customer-orders]').within(() => {
                cy.get('[data-context=erp-table] thead tr').checkRow(
                    [
                        'Commande',
                        'Commandé le',
                        '<PERSON><PERSON><PERSON> annoncée',
                        'Client',
                        'Montant',
                        'Suivi',
                        'Paiements',
                        'Dernier message interne',
                    ],
                    { cell_selector: 'th' },
                )

                cy.get('[data-context=erp-table] tbody')
                    .children('tr')
                    .checkRows(
                        [
                            [
                                ['3805729', 'Payée'],
                                '21/03/2025',
                                ['31/03/2025', '5 jrs.'],
                                ['Corine CHTITGOUTE', 'Pas content! pas content!'],
                                '111,70 €',
                                'Nouveau',
                                ['111,70 €', 'CBOL-Ogone-3DS (remise)'],
                                ['Alex TERRIEUR', 'gna gna gna'],
                            ],
                            [['Muse M-1802 DJ', 'JBL GO 4 Noir']],
                            [
                                ['3805727', 'Facturée'],
                                '22/03/2025',
                                ['26/03/2025', 'Pas de délai'],
                                'Corine CHTITGOUTE',
                                '396,90 €',
                                'Nouveau',
                                ['396,90 €', 'CBOL-Ogone-3DS (remise)'],
                                '',
                            ],
                            ['Cambridge Audio DacMagic 200M Silver'],
                        ],
                        { selector_method: 'children' },
                    )
            })
        })

        it('should use url params', function () {
            cy.visit(PAGE + '?pager[filters]=%7B%22overdue_statuses%22%3A%5B%22NEW%22%2C%22ONGOING%22%5D%7D')
            cy.toggleMenu()

            cy.wait('@cpost_customer_orders').then((xhr) => {
                expect(xhr.request.body.where).to.deep.equal({
                    _and: [
                        {
                            overdue_status: {
                                _in: ['NEW', 'ONGOING'],
                            },
                        },
                    ],
                })
            })
            cy.wait('@get_statuses')
        })
    })

    describe('Table filter', function () {
        it('should filter successfully', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@cpost_customer_orders').then((xhr) => {
                expect(xhr.request.body.where).to.deep.equal({
                    _and: [
                        {
                            overdue_status: {
                                _in: ['NEW'],
                            },
                        },
                    ],
                })
            })
            cy.wait('@get_statuses')

            cy.get('[data-context=customer-orders]').within(() => {
                cy.get('[data-context=overdue-status-selector]').erpMultiselect('', 'En cours')
                cy.get('[data-context=erp-form-label]').click() // close multiselect
                cy.get('[data-context="filters-submit-btn"]').click()

                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.where).to.deep.equal({
                        _and: [
                            {
                                overdue_status: {
                                    _in: ['NEW', 'ONGOING'],
                                },
                            },
                        ],
                    })
                })

                cy.url().should(
                    'include',
                    'pager[filters]=%7B%22overdue_statuses%22%3A%5B%22NEW%22%2C%22ONGOING%22%5D%7D',
                ) // ["NEW","ONGOING"]

                cy.get('[data-context=overdue-status-selector]').erpMultiselect('', 'Traité')
                cy.get('[data-context=erp-form-label]').click() // close multiselect
                cy.get('[data-context="filters-submit-btn"]').click()

                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.where).to.deep.equal({
                        _and: [
                            {
                                overdue_status: {
                                    _in: ['NEW', 'ONGOING', 'FINISHED'],
                                },
                            },
                        ],
                    })
                })

                cy.url().should(
                    'include',
                    'pager[filters]=%7B%22overdue_statuses%22%3A%5B%22NEW%22%2C%22ONGOING%22%2C%22FINISHED%22%5D%7D',
                ) // ["NEW","ONGOING","FINISHED"]
            })
        })
    })

    describe('Table sort', function () {
        it('should sort the table', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=customer-orders] [data-context=erp-table] > thead tr').within(() => {
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('customer_order_id')
                    expect(xhr.request.body.order_direction).to.equal('desc')
                })

                cy.get('th:contains(Commande)').click()
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('customer_order_id')
                    expect(xhr.request.body.order_direction).to.equal('asc')
                })

                cy.get('th:contains(Commandé le)').click()
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('created_at')
                    expect(xhr.request.body.order_direction).to.equal('desc')
                })

                cy.get('th:contains(Commandé le)').click()
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('created_at')
                    expect(xhr.request.body.order_direction).to.equal('asc')
                })

                cy.get('th:contains(Livraison annoncée)').click()
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('initial_estimated_delivery_date')
                    expect(xhr.request.body.order_direction).to.equal('desc')
                })

                cy.get('th:contains(Livraison annoncée)').click()
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('initial_estimated_delivery_date')
                    expect(xhr.request.body.order_direction).to.equal('asc')
                })

                cy.get('th:contains(Montant)').click()
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('amount_all_tax_included')
                    expect(xhr.request.body.order_direction).to.equal('desc')
                })

                cy.get('th:contains(Montant)').click()
                cy.wait('@cpost_customer_orders').then((xhr) => {
                    expect(xhr.request.body.order_by).to.equal('amount_all_tax_included')
                    expect(xhr.request.body.order_direction).to.equal('asc')
                })
            })
        })
    })

    describe('Product availability slide-in', function () {
        it('show tha slide-in', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait(['@cpost_customer_orders', '@get_statuses'])

            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                fixture: 'erp/customer-order-products/delivered_customer_order.json',
            }).as('cpost_customer_order_products')

            cy.get('[data-context=erp-table] tbody')
                .children('tr')
                .eq(0)
                .find('[data-context=open-availability-button]')
                .as('button')

            cy.get('[data-context=products-availability]').should('not.exist')
            cy.get('@button').tooltip('Voir les disponibilités produit')
            cy.get('@button').click()
            cy.wait('@cpost_customer_order_products')
            cy.get('[data-context=products-availability]').should('be.visible')
            cy.get('[data-context=slide-out-container-close-btn]').click()
        })
    })
})
